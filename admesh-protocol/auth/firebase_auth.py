from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, Security
from fastapi.security import <PERSON>TT<PERSON><PERSON>orizationCredentials, <PERSON>TT<PERSON><PERSON><PERSON><PERSON>
from firebase_admin import auth
import firebase_admin

# Use centralized Firebase initialization
from firebase.config import initialize_firebase

# Initialize Firebase using centralized configuration
if not firebase_admin._apps:
    initialize_firebase()

security = HTTPBearer()

def create_firebase_user(email: str, password: str):
    try:
        user = auth.create_user(
            email=email,
            password=password,
            email_verified=False
        )
        return user
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

async def get_current_user(credentials: HTTPAuthorizationCredentials = Security(security)):
    try:
        token = credentials.credentials
        decoded_token = auth.verify_id_token(token)
        return decoded_token
    except Exception as e:
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication credentials"
        )

def verify_role(token: dict, required_role: str):
    if token.get("role") != required_role:
        raise HTTPException(
            status_code=403,
            detail=f"Only {required_role}s can access this endpoint"
        )
