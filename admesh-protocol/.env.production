# Production Environment Configuration (for actual deployment)
# Environment
ENV=production
DEBUG=false
LOG_LEVEL=WARNING
PORT=8080

# Firebase Configuration (production)
GOOGLE_APPLICATION_CREDENTIALS=./firebase/serviceAccountKey.json

# API Configuration
#SITE_URL=https://api.useadmesh.com
SITE_URL=http://127.0.0.1:8000


# External Services
OPENAI_API_KEY=**************************************************************************************************************************************************
OPENROUTER_API_KEY=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
RESEND_API_KEY=re_EqBocs67_QFgdUbbUEkVvGeommYFp2wXQ

# Production IDs and Keys
NEXT_PUBLIC_AGENT_ID=ZAinjf9SuPYCk7u8r0ZEeKY2fV42
NEXT_PUBLIC_USER_ID=C8SMDERS3naFG2k103EDBacoTCy2
# TODO: Replace with actual production API key
NEXT_PUBLIC_AGENT_API_KEY=sk_prod_your_production_api_key_here

# Stripe Configuration (Production)
# TODO: Replace with actual production keys
STRIPE_SECRET_KEY=sk_live_your_production_stripe_key_here
STRIPE_DOMAIN=https://useadmesh.com
STRIPE_WEBHOOK_SECRET=whsec_your_production_webhook_secret_here

# Frontend URLs
FRONTEND_URL=https://useadmesh.com
FRONTEND_URL_PROD=https://useadmesh.com

# Security
FERNET_SECRET=VlWiQCfcL7LW34MpCN9UCmWHU0F2cUQecgiQBtGeCTM=

# Feature Flags (Production)
ENABLE_ANALYTICS=true
ENABLE_RATE_LIMITING=true
ENABLE_CACHING=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_TRUST_SCORE_THROTTLING=true
NEXT_PUBLIC_AGENT_API_KEY=sk_prod_your_production_api_key_here

# Production settings
DEBUG=false
LOG_LEVEL=WARNING
PORT=8000
