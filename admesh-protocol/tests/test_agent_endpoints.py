import requests
import os
import json
from datetime import datetime, timedelta
import firebase_admin
from firebase_admin import auth
import time
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Initialize Firebase Admin SDK for testing using centralized configuration
if not firebase_admin._apps:
    from firebase.config import initialize_firebase
    initialize_firebase()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
TEST_AGENT_EMAIL = "<EMAIL>"
TEST_AGENT_PASSWORD = "testpassword123"

def get_firebase_token(email, password):
    """Get a Firebase ID token for testing"""
    # This is a simplified version - in a real test, you would use Firebase Auth REST API
    # to get a token, or use a pre-generated token for a test user
    try:
        user = auth.get_user_by_email(email)
    except:
        user = auth.create_user(
            email=email,
            password=password,
            email_verified=True
        )
        
        # Set custom claims for agent role
        auth.set_custom_user_claims(user.uid, {"role": "agent"})
        
        # Wait for claims to propagate
        time.sleep(2)
    
    # In a real test, you would get a token via Firebase Auth REST API
    # For this example, we'll just print instructions
    print(f"Please get a token for {email} and set it in the TOKEN variable")
    return None

# Set your token here after getting it from Firebase Auth
TOKEN = None

def test_agent_queries_endpoint():
    """Test the agent/queries endpoint"""
    if not TOKEN:
        print("Please set a valid Firebase token in the TOKEN variable")
        return
    
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    
    response = requests.get(f"{API_BASE_URL}/agent/queries?time_range=30d", headers=headers)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(json.dumps(data, indent=2))
    else:
        print(f"Error: {response.text}")

def test_agent_conversions_endpoint():
    """Test the agent/conversions endpoint"""
    if not TOKEN:
        print("Please set a valid Firebase token in the TOKEN variable")
        return
    
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    
    response = requests.get(f"{API_BASE_URL}/agent/conversions?time_range=30d", headers=headers)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(json.dumps(data, indent=2))
    else:
        print(f"Error: {response.text}")

def test_agent_stats_endpoint():
    """Test the agent/stats endpoint"""
    if not TOKEN:
        print("Please set a valid Firebase token in the TOKEN variable")
        return
    
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    
    response = requests.get(f"{API_BASE_URL}/agent/stats?time_range=30d", headers=headers)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(json.dumps(data, indent=2))
    else:
        print(f"Error: {response.text}")

if __name__ == "__main__":
    # Uncomment to get a token
    # get_firebase_token(TEST_AGENT_EMAIL, TEST_AGENT_PASSWORD)
    
    # Run tests
    print("Testing agent/queries endpoint:")
    test_agent_queries_endpoint()
    
    print("\nTesting agent/conversions endpoint:")
    test_agent_conversions_endpoint()
    
    print("\nTesting agent/stats endpoint:")
    test_agent_stats_endpoint()
